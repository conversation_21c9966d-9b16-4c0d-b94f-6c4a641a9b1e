import * as Localization from 'expo-localization';
import { Language } from './types';

// 翻译文本定义
export const translations = {
  en: {
    // 通用
    common: {
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      ok: 'OK',
      confirm: 'Confirm',
      loading: 'Loading...',
      connecting: 'Connecting...',
      error: 'Error',
      success: 'Success',
      name: 'Name',
      url: 'URL',
      username: '<PERSON>rna<PERSON>',
      password: 'Password',
      protocol: 'Protocol',
      certificate: 'Certificate',
      group: 'Group',
      all: 'All',
      default: 'Default',
      deleteConfirmMessage: 'Are you sure you want to delete this configuration?',
      deleteError: 'Failed to delete configuration',
      import: 'Import',
      importConfig: 'Import Configuration',
      importConfigFrom: 'Import Configuration From',
      selectConfigToImport: 'Select Configuration to Import',
      importSuccess: 'Configuration imported successfully',
      importError: 'Failed to import configuration',
      unsupportedType: 'Unsupported configuration type',
    },
    
    // 导航
    navigation: {
      home: 'Home',
      settings: 'Settings',
      addConfig: 'Add Configuration',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'UI Manager',
      addConfiguration: 'Add Configuration',
      editGroups: 'Edit Groups',
      noConfigurations: 'No configurations yet',
      addFirstConfig: 'Add your first configuration',
    },
    
    // 添加配置
    addConfig: {
      title: 'Add Configuration',
      selectType: 'Select Configuration Type',
      configName: 'Configuration Name',
      apiKey: 'API Key',
      httpWarning: 'This means API and other sensitive information will be transmitted in plain text. Please ensure you are in a secure network environment.',
      certTooltip: 'Please fill in if using self-signed certificate',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: 'Select Group',
      submit: 'Add Configuration',
    },

    // 配置表单
    configForm: {
      name: 'Configuration Name',
      namePlaceholder: 'Enter configuration name',
      protocol: 'Protocol',
      url: 'URL',
      username: 'Username',
      usernamePlaceholder: 'Enter username',
      password: 'Password',
      passwordPlaceholder: 'Enter password',
      api: 'API Key',
      apiPlaceholder: 'Enter API key',
      cert: 'Certificate',
      certTooltip: 'Please fill in if using self-signed certificate',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: 'Public Key Hash (SHA256)',
      publicKeyHashTooltip: 'Enter SHA256 public key hashes in hexadecimal format, one per line. The client will automatically convert them to base64.',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: 'Group',
      groupHint: 'Configuration will be automatically added to "All" group. You can optionally select additional groups.',
      httpWarning: 'This means API and other sensitive information will be transmitted in plain text. Please ensure you are in a secure network environment.',
      httpWarningTitle: 'Security Warning',
      connectionFailed: 'Failed to connect to the panel. Please check your configuration.',
      submitError: 'Failed to submit configuration. Please try again.',
      addSUIConfig: 'Add S-UI Configuration',
      editSUIConfig: 'Edit S-UI Configuration',
      addXUIConfig: 'Add X-UI Configuration',
      editXUIConfig: 'Edit X-UI Configuration',
      add3XUIConfig: 'Add 3X-UI Configuration',
      edit3XUIConfig: 'Edit 3X-UI Configuration',
    },

    // 验证
    validation: {
      required: 'This field is required',
      invalidUrl: 'Please enter a valid URL',
    },
    
    // 分组管理
    groups: {
      title: 'Manage Groups',
      all: 'All',
      addGroup: 'Add Group',
      rename: 'Rename',
      moveUp: 'Move Up',
      moveDown: 'Move Down',
      deleteGroup: 'Delete Group',
      deleteConfirm: 'Are you sure you want to delete this group? All configurations in this group will be moved to the all group.',
      groupName: 'Group Name',
      enterGroupName: 'Enter group name',
      renameGroup: 'Rename Group',
      newGroupName: 'New group name',
      cannotDeleteDefault: 'Cannot delete all group',
      cannotRenameDefault: 'Cannot rename all group',
    },
    
    // 设置
    settings: {
      title: 'Settings',
      theme: 'Theme',
      language: 'Language',
      proPlan: 'Pro Plan',
      subscribeToPro: 'Subscribe to Pro',
      messengerGroup: 'Messenger Group',
      themes: {
        light: 'Light',
        dark: 'Dark',
        system: 'Follow System',
      },
      languages: {
        en: 'English',
        'zh-CN': 'Simplified Chinese',
        'zh-TW': 'Traditional Chinese',
        fa: 'Persian',
      },
    },
  },
  
  'zh-CN': {
    // 通用
    common: {
      add: '添加',
      edit: '编辑',
      delete: '删除',
      save: '保存',
      cancel: '取消',
      ok: '确定',
      confirm: '确认',
      loading: '加载中...',
      connecting: '连接中...',
      error: '错误',
      success: '成功',
      name: '名称',
      url: '地址',
      username: '用户名',
      password: '密码',
      protocol: '协议',
      certificate: '证书',
      group: '分组',
      all: '全部',
      default: '默认',
      deleteConfirmMessage: '确定要删除此配置吗？',
      deleteError: '删除配置失败',
      import: '导入',
      importConfig: '导入配置',
      importConfigFrom: '从配置导入',
      selectConfigToImport: '选择要导入的配置',
      importSuccess: '配置导入成功',
      importError: '配置导入失败',
      unsupportedType: '不支持的配置类型',
    },
    
    // 导航
    navigation: {
      home: '主页',
      settings: '设置',
      addConfig: '添加配置',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'UI 管理器',
      addConfiguration: '添加配置',
      editGroups: '编辑分组',
      noConfigurations: '暂无配置',
      addFirstConfig: '添加您的第一个配置',
    },
    
    // 添加配置
    addConfig: {
      title: '添加配置',
      selectType: '选择配置类型',
      configName: '配置名称',
      apiKey: 'API 密钥',
      httpWarning: '这意味着 API 等敏感信息以明文传输，请确保在安全的网络环境中使用。',
      certTooltip: '如果为自签证书请填写',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: '选择分组',
      submit: '添加配置',
    },

    // 配置表单
    configForm: {
      name: '配置名称',
      namePlaceholder: '请输入配置名称',
      protocol: '协议',
      url: '地址',
      username: '用户名',
      usernamePlaceholder: '请输入用户名',
      password: '密码',
      passwordPlaceholder: '请输入密码',
      api: 'API 密钥',
      apiPlaceholder: '请输入 API 密钥',
      cert: '证书',
      certTooltip: '如果为自签证书请填写',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: '公钥哈希 (SHA256)',
      publicKeyHashTooltip: '请输入十六进制格式的SHA256公钥哈希，每行一个。客户端会自动转换为base64格式。',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: '分组',
      groupHint: '配置将自动添加到"全部"分组。您可以选择性地添加到其他分组。',
      httpWarning: '这意味着 API 等敏感信息以明文传输，请确保在安全的网络环境中使用。',
      httpWarningTitle: '安全警告',
      connectionFailed: '连接面板失败，请检查配置信息。',
      submitError: '提交配置失败，请重试。',
      addSUIConfig: '添加 S-UI 配置',
      editSUIConfig: '编辑 S-UI 配置',
      addXUIConfig: '添加 X-UI 配置',
      editXUIConfig: '编辑 X-UI 配置',
      add3XUIConfig: '添加 3X-UI 配置',
      edit3XUIConfig: '编辑 3X-UI 配置',
    },

    // 验证
    validation: {
      required: '此字段为必填项',
      invalidUrl: '请输入有效的 URL',
    },
    
    // 分组管理
    groups: {
      title: '管理分组',
      all: '全部',
      addGroup: '添加分组',
      rename: '重命名',
      moveUp: '上移',
      moveDown: '下移',
      deleteGroup: '删除分组',
      deleteConfirm: '确定要删除此分组吗？该分组下的所有配置将移动到全部分组。',
      groupName: '分组名称',
      enterGroupName: '请输入分组名称',
      renameGroup: '重命名分组',
      newGroupName: '新分组名称',
      cannotDeleteDefault: '无法删除全部分组',
      cannotRenameDefault: '无法重命名全部分组',
    },
    
    // 设置
    settings: {
      title: '设置',
      theme: '主题',
      language: '语言',
      proPlan: 'Pro 计划',
      subscribeToPro: '订阅 Pro',
      messengerGroup: 'Messenger 群组',
      themes: {
        light: '浅色',
        dark: '深色',
        system: '跟随系统',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁体中文',
        fa: 'فارسی',
      },
    },
  },
  
  'zh-TW': {
    // 通用
    common: {
      add: '新增',
      edit: '編輯',
      delete: '刪除',
      save: '儲存',
      cancel: '取消',
      ok: '確定',
      confirm: '確認',
      loading: '載入中...',
      connecting: '連接中...',
      error: '錯誤',
      success: '成功',
      name: '名稱',
      url: '網址',
      username: '使用者名稱',
      password: '密碼',
      protocol: '協定',
      certificate: '憑證',
      group: '群組',
      all: '全部',
      default: '預設',
      deleteConfirmMessage: '確定要刪除此設定嗎？',
      deleteError: '刪除設定失敗',
    },
    
    // 导航
    navigation: {
      home: '主頁',
      settings: '設定',
      addConfig: '新增設定',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'UI 管理器',
      addConfiguration: '新增設定',
      editGroups: '編輯群組',
      noConfigurations: '暫無設定',
      addFirstConfig: '新增您的第一個設定',
    },
    
    // 添加配置
    addConfig: {
      title: '新增設定',
      selectType: '選擇設定類型',
      configName: '設定名稱',
      apiKey: 'API 金鑰',
      httpWarning: '這意味著 API 等敏感資訊以明文傳輸，請確保在安全的網路環境中使用。',
      certTooltip: '如果為自簽憑證請填寫',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: '選擇群組',
      submit: '新增設定',
    },

    // 配置表单
    configForm: {
      name: '設定名稱',
      namePlaceholder: '請輸入設定名稱',
      protocol: '協定',
      url: '網址',
      username: '使用者名稱',
      usernamePlaceholder: '請輸入使用者名稱',
      password: '密碼',
      passwordPlaceholder: '請輸入密碼',
      api: 'API 金鑰',
      apiPlaceholder: '請輸入 API 金鑰',
      cert: '憑證',
      certTooltip: '如果為自簽憑證請填寫',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: '公鑰雜湊 (SHA256)',
      publicKeyHashTooltip: '請輸入十六進位格式的SHA256公鑰雜湊，每行一個。客戶端會自動轉換為base64格式。',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: '群組',
      groupHint: '設定將自動新增到「全部」群組。您可以選擇性地新增到其他群組。',
      httpWarning: '這意味著 API 等敏感資訊以明文傳輸，請確保在安全的網路環境中使用。',
      httpWarningTitle: '安全警告',
      connectionFailed: '連接面板失敗，請檢查設定資訊。',
      submitError: '提交設定失敗，請重試。',
      addSUIConfig: '新增 S-UI 設定',
      editSUIConfig: '編輯 S-UI 設定',
      addXUIConfig: '新增 X-UI 設定',
      editXUIConfig: '編輯 X-UI 設定',
      add3XUIConfig: '新增 3X-UI 設定',
      edit3XUIConfig: '編輯 3X-UI 設定',
    },

    // 验证
    validation: {
      required: '此欄位為必填項',
      invalidUrl: '請輸入有效的 URL',
    },
    
    // 分组管理
    groups: {
      title: '管理群組',
      all: '全部',
      addGroup: '新增群組',
      rename: '重新命名',
      moveUp: '上移',
      moveDown: '下移',
      deleteGroup: '刪除群組',
      deleteConfirm: '確定要刪除此群組嗎？該群組下的所有設定將移動到全部群組。',
      groupName: '群組名稱',
      enterGroupName: '請輸入群組名稱',
      renameGroup: '重新命名群組',
      newGroupName: '新群組名稱',
      cannotDeleteDefault: '無法刪除全部群組',
      cannotRenameDefault: '無法重新命名全部群組',
    },
    
    // 设置
    settings: {
      title: '設定',
      theme: '主題',
      language: '語言',
      proPlan: 'Pro 方案',
      subscribeToPro: '訂閱 Pro',
      messengerGroup: 'Messenger 群組',
      themes: {
        light: '淺色',
        dark: '深色',
        system: '跟隨系統',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁體中文',
        fa: 'فارسی',
      },
    },
  },
  
  fa: {
    // 通用
    common: {
      add: 'افزودن',
      edit: 'ویرایش',
      delete: 'حذف',
      save: 'ذخیره',
      cancel: 'لغو',
      ok: 'تأیید',
      confirm: 'تأیید',
      loading: 'در حال بارگذاری...',
      connecting: 'در حال اتصال...',
      error: 'خطا',
      success: 'موفقیت',
      name: 'نام',
      url: 'آدرس',
      username: 'نام کاربری',
      password: 'رمز عبور',
      protocol: 'پروتکل',
      certificate: 'گواهی',
      group: 'گروه',
      all: 'همه',
      default: 'پیش‌فرض',
      deleteConfirmMessage: 'آیا مطمئن هستید که می‌خواهید این پیکربندی را حذف کنید؟',
      deleteError: 'حذف پیکربندی ناموفق بود',
    },
    
    // 导航
    navigation: {
      home: 'خانه',
      settings: 'تنظیمات',
      addConfig: 'افزودن پیکربندی',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'مدیر UI',
      addConfiguration: 'افزودن پیکربندی',
      editGroups: 'ویرایش گروه‌ها',
      noConfigurations: 'هنوز پیکربندی وجود ندارد',
      addFirstConfig: 'اولین پیکربندی خود را اضافه کنید',
    },
    
    // 添加配置
    addConfig: {
      title: 'افزودن پیکربندی',
      selectType: 'انتخاب نوع پیکربندی',
      configName: 'نام پیکربندی',
      apiKey: 'کلید API',
      httpWarning: 'این بدان معناست که API و سایر اطلاعات حساس به صورت متن ساده ارسال می‌شوند. لطفاً اطمینان حاصل کنید که در محیط شبکه امنی هستید.',
      certTooltip: 'اگر از گواهی خودامضا استفاده می‌کنید، لطفاً پر کنید',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: 'انتخاب گروه',
      submit: 'افزودن پیکربندی',
    },

    // 配置表单
    configForm: {
      name: 'نام پیکربندی',
      namePlaceholder: 'نام پیکربندی را وارد کنید',
      protocol: 'پروتکل',
      url: 'آدرس',
      username: 'نام کاربری',
      usernamePlaceholder: 'نام کاربری را وارد کنید',
      password: 'رمز عبور',
      passwordPlaceholder: 'رمز عبور را وارد کنید',
      api: 'کلید API',
      apiPlaceholder: 'کلید API را وارد کنید',
      cert: 'گواهی',
      certTooltip: 'اگر از گواهی خودامضا استفاده می‌کنید، لطفاً پر کنید',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      publicKeyHash: 'هش کلید عمومی (SHA256)',
      publicKeyHashTooltip: 'هش‌های کلید عمومی SHA256 را در فرمت هگزادسیمال وارد کنید، هر خط یکی. کلاینت به طور خودکار آنها را به base64 تبدیل می‌کند.',
      publicKeyHashPlaceholder: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456\n9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba',
      group: 'گروه',
      groupHint: 'پیکربندی به طور خودکار به گروه "همه" اضافه خواهد شد. می‌توانید به صورت اختیاری گروه‌های اضافی انتخاب کنید.',
      httpWarning: 'این بدان معناست که API و سایر اطلاعات حساس به صورت متن ساده ارسال می‌شوند. لطفاً اطمینان حاصل کنید که در محیط شبکه امنی هستید.',
      httpWarningTitle: 'هشدار امنیتی',
      connectionFailed: 'اتصال به پنل ناموفق بود. لطفاً تنظیمات خود را بررسی کنید.',
      submitError: 'ارسال پیکربندی ناموفق بود. لطفاً دوباره تلاش کنید.',
      addSUIConfig: 'افزودن پیکربندی S-UI',
      editSUIConfig: 'ویرایش پیکربندی S-UI',
      addXUIConfig: 'افزودن پیکربندی X-UI',
      editXUIConfig: 'ویرایش پیکربندی X-UI',
      add3XUIConfig: 'افزودن پیکربندی 3X-UI',
      edit3XUIConfig: 'ویرایش پیکربندی 3X-UI',
    },

    // 验证
    validation: {
      required: 'این فیلد الزامی است',
      invalidUrl: 'لطفاً یک URL معتبر وارد کنید',
    },
    
    // 分组管理
    groups: {
      title: 'مدیریت گروه‌ها',
      all: 'همه',
      addGroup: 'افزودن گروه',
      rename: 'تغییر نام',
      moveUp: 'انتقال به بالا',
      moveDown: 'انتقال به پایین',
      deleteGroup: 'حذف گروه',
      deleteConfirm: 'آیا مطمئن هستید که می‌خواهید این گروه را حذف کنید؟ تمام پیکربندی‌های این گروه به گروه همه منتقل خواهند شد.',
      groupName: 'نام گروه',
      enterGroupName: 'نام گروه را وارد کنید',
      renameGroup: 'تغییر نام گروه',
      newGroupName: 'نام جدید گروه',
      cannotDeleteDefault: 'نمی‌توان گروه همه را حذف کرد',
      cannotRenameDefault: 'نمی‌توان نام گروه همه را تغییر داد',
    },
    
    // 设置
    settings: {
      title: 'تنظیمات',
      theme: 'تم',
      language: 'زبان',
      proPlan: 'طرح Pro',
      subscribeToPro: 'اشتراک Pro',
      messengerGroup: 'گروه Messenger',
      themes: {
        light: 'روشن',
        dark: 'تیره',
        system: 'پیروی از سیستم',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁體中文',
        fa: 'فارسی',
      },
    },
  },
};

// 获取当前语言
export const getCurrentLanguage = (): Language => {
  const locale = Localization.getLocales()[0];
  const languageTag = locale.languageTag;
  
  if (languageTag.startsWith('zh')) {
    return locale.regionCode === 'TW' || locale.regionCode === 'HK' ? 'zh-TW' : 'zh-CN';
  }
  
  if (languageTag.startsWith('fa')) {
    return 'fa';
  }
  
  return 'en';
};

// 翻译函数
export const t = (key: string, language: Language = 'en'): string => {
  const keys = key.split('.');
  let value: any = translations[language];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // 如果找不到翻译，回退到英文
      value = translations.en;
      for (const fallbackKey of keys) {
        if (value && typeof value === 'object' && fallbackKey in value) {
          value = value[fallbackKey];
        } else {
          return key; // 如果英文也没有，返回原始 key
        }
      }
      break;
    }
  }
  
  return typeof value === 'string' ? value : key;
};
