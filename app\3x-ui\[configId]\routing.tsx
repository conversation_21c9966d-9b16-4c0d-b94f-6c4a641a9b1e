import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAppStore } from '@/lib/store';
import { ThreeXUIConfig } from '@/lib/types';
import { getThreeXUIXrayConfig } from '@/panels/3x-ui/utils';
import { smartFetch } from '@/lib/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { Plus, Save, RotateCcw, GripVertical, Database } from 'lucide-react-native';
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { SafeAreaView, StyleSheet, View, Alert, TouchableOpacity } from 'react-native';
import DragList, { DragListRenderItemInfo } from 'react-native-draglist';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';

// 路由规则类型定义（不再使用id，直接用索引）
interface RouteRule {
  domainMatcher?: 'hybrid' | 'linear';
  type: 'field';
  domain?: string[];
  ip?: string[];
  port?: string;
  sourcePort?: string;
  network?: 'tcp' | 'udp' | 'tcp,udp';
  source?: string[];
  user?: string[];
  inboundTag?: string[];
  protocol?: ('http' | 'tls' | 'quic' | 'bittorrent')[];
  attrs?: Record<string, string>;
  outboundTag?: string;
  balancerTag?: string;
  ruleTag?: string;
}

// Geo文件更新进度类型
interface GeoUpdateProgress {
  fileName: string;
  completed: boolean;
  success?: boolean;
  error?: string;
}

// Geo文件更新结果类型
interface GeoUpdateResult {
  totalFiles: number;
  completedFiles: number;
  successFiles: number;
  failedFiles: number;
  results: GeoUpdateProgress[];
}

export default function RoutingScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  const { configs, getServerConfig } = useAppStore();

  const [rules, setRules] = useState<RouteRule[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  // 虚拟顺序状态：存储规则原始索引的排序数组
  const [virtualOrder, setVirtualOrder] = useState<string[]>([]);

  // Geo更新相关状态
  const [isUpdatingGeo, setIsUpdatingGeo] = useState(false);
  const [geoUpdateProgress, setGeoUpdateProgress] = useState<GeoUpdateProgress[]>([]);
  const [geoUpdatePercentage, setGeoUpdatePercentage] = useState(0);

  // 重启相关状态
  const [isRestarting, setIsRestarting] = useState(false);

  // Bottom sheet 相关状态
  const [selectedRuleIndex, setSelectedRuleIndex] = useState<number>(-1);
  const actionSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['15%'], []);

  // 获取当前配置
  const currentConfig = configs.find(c => c.id === configId) as ThreeXUIConfig;
  const serverConfig = getServerConfig(configId || '');

  // Bottom sheet 渲染函数
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // 根据虚拟顺序获取排序后的规则数组（根据原始索引）
  const getOrderedRules = useCallback(() => {
    return virtualOrder
      .map((originalIndex) => rules[parseInt(originalIndex, 10)])
      .filter((rule) => rule !== undefined) as RouteRule[];
  }, [virtualOrder, rules]);

  // 加载xray配置
  const loadXrayConfig = useCallback(async () => {
    if (!currentConfig) return;

    try {
      await getThreeXUIXrayConfig(currentConfig);
    } catch (error) {
      Alert.alert('错误', '加载路由配置失败');
    }
  }, [currentConfig]);

  // 页面聚焦时加载数据
  useFocusEffect(
    useCallback(() => {
      // 添加延迟确保从其他页面返回时能正确刷新数据
      loadXrayConfig();
    }, [loadXrayConfig])
  );

  // 加载路由规则
  useEffect(() => {
    if (serverConfig?.xray?.routing?.rules) {
      const rawRules = serverConfig.xray.routing.rules as RouteRule[];
      setRules(rawRules);
      // 初始化虚拟顺序为原始索引顺序
      setVirtualOrder(rawRules.map((_, index) => index.toString()));
      setHasChanges(false);
    } else {
      setRules([]);
      setVirtualOrder([]);
      setHasChanges(false);
    }
  }, [serverConfig, serverConfig?.xray?.routing?.rules]);

  // 添加规则
  const handleAddRule = () => {
    router.push({
      pathname: '/3x-ui/rule-config',
      params: { configId }
    });
  };

  // 处理卡片点击 - 显示 bottom sheet
  const handleRulePress = (virtualIndex: number) => {
    setSelectedRuleIndex(virtualIndex);
    actionSheetRef.current?.present();
  };

  // 处理编辑
  const handleEdit = () => {
    if (selectedRuleIndex >= 0) {
      actionSheetRef.current?.dismiss();
      // 获取虚拟位置对应的原始索引
      const originalIndex = virtualOrder[selectedRuleIndex];

      router.push({
        pathname: '/3x-ui/rule-config',
        params: { configId, ruleIndex: originalIndex }
      });
    }
  };

  // 处理删除确认
  const handleDeleteConfirm = () => {
    Alert.alert(
      '确认删除',
      '确定要删除此路由规则吗？此操作不可撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: handleDelete
        }
      ]
    );
  };

  // 处理删除规则 - 在虚拟排序中删除相应index
  const handleDelete = () => {
    if (selectedRuleIndex >= 0) {
      actionSheetRef.current?.dismiss();

      // 从虚拟顺序中移除选中的索引
      const newVirtualOrder = [...virtualOrder];
      newVirtualOrder.splice(selectedRuleIndex, 1);
      setVirtualOrder(newVirtualOrder);
      setHasChanges(true);
      setSelectedRuleIndex(-1);
    }
  };

  // 拖拽重排序 - 只修改虚拟顺序
  const handleReorder = async (fromIndex: number, toIndex: number) => {
    const newVirtualOrder = [...virtualOrder];
    const movedRuleId = newVirtualOrder.splice(fromIndex, 1)[0];
    newVirtualOrder.splice(toIndex, 0, movedRuleId);
    setVirtualOrder(newVirtualOrder);
    setHasChanges(true);
  };

  // 保存规则
  const handleSave = async () => {
    if (!currentConfig) {
      Alert.alert('错误', '未找到配置信息');
      return;
    }

    try {
      const updatedServerConfig = { ...serverConfig };
      if (!updatedServerConfig.xray) updatedServerConfig.xray = {};
      if (!updatedServerConfig.xray.routing) updatedServerConfig.xray.routing = {};

      // 按照虚拟顺序重新排列规则（直接使用原始索引）
      const reorderedRules = virtualOrder
        .map((idxStr) => rules[parseInt(idxStr, 10)])
        .filter((rule) => rule !== undefined) as RouteRule[];

      updatedServerConfig.xray.routing.rules = reorderedRules;

      // 组装完整的 xraySetting（参考出站配置的实现）
      const xraySettingObj = {
        ...(updatedServerConfig.xray || {}),
        routing: {
          ...(updatedServerConfig.xray.routing || {}),
          rules: reorderedRules,
        },
      };

      // 同步到后端：POST /panel/xray/update
      const formData = new FormData();
      formData.append('xraySetting', JSON.stringify(xraySettingObj));

      const response = await smartFetch(
        `${currentConfig.protocol}://${currentConfig.url}/panel/xray/update`,
        { method: 'POST', body: formData },
        currentConfig
      );

      const result = await response.json();
      if (result.success) {
        // 更新本地缓存的 xray 配置（从后端再拉一遍，保证一致）
        await getThreeXUIXrayConfig(currentConfig);
        setHasChanges(false);
        Alert.alert('成功', '路由规则已保存');
      } else {
        Alert.alert('错误', result.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save routing rules failed:', error);
      Alert.alert('错误', '保存失败');
    }
  };

  // 重启Xray服务
  const handleRestartXray = async () => {
    if (!currentConfig) return;

    setIsRestarting(true);
    try {
      const baseUrl = `${currentConfig.protocol}://${currentConfig.url}`;
      const restartUrl = `${baseUrl}/server/restartXrayService`;

      const response = await smartFetch(restartUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      }, currentConfig);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        Alert.alert('成功', 'Xray服务重启成功');
      } else {
        Alert.alert('错误', 'Xray服务重启失败');
      }
    } catch (error) {
      Alert.alert('错误', 'Xray服务重启失败');
    } finally {
      setIsRestarting(false);
    }
  };

  // 重启服务确认
  const handleRestart = () => {
    Alert.alert(
      '确认重启',
      '确定要重启Xray服务吗？这将应用新的路由规则配置。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重启',
          onPress: handleRestartXray
        }
      ]
    );
  };

  // 更新单个geo文件
  const updateSingleGeoFile = async (fileName: string): Promise<GeoUpdateProgress> => {
    try {
      if (!currentConfig) {
        throw new Error('配置信息不存在');
      }

      const baseUrl = `${currentConfig.protocol}://${currentConfig.url}`;
      const updateUrl = `${baseUrl}/server/updateGeofile/${fileName}`;

      const requestOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        credentials: 'include'
      };

      const response = await smartFetch(updateUrl, requestOptions, currentConfig);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      return {
        fileName,
        completed: true,
        success: result.success === true,
        error: result.success ? undefined : result.msg || '更新失败'
      };
    } catch (error) {
      console.error(`Update ${fileName} failed:`, error);
      return {
        fileName,
        completed: true,
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  };

  // 更新geo数据库
  const handleUpdateGeo = () => {
    Alert.alert(
      '确认更新',
      '确定要更新geo数据库吗？这将下载最新的geosite和geoip数据。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '更新',
          onPress: updateGeoFiles
        }
      ]
    );
  };

  // 执行geo文件更新
  const updateGeoFiles = async () => {
    const geoFiles = [
      'geosite.dat',
      'geoip.dat',
      'geosite_IR.dat',
      'geoip_IR.dat',
      'geosite_RU.dat',
      'geoip_RU.dat'
    ];

    setIsUpdatingGeo(true);
    setGeoUpdateProgress([]);
    setGeoUpdatePercentage(0);

    const results: GeoUpdateProgress[] = [];

    try {
      for (let i = 0; i < geoFiles.length; i++) {
        const fileName = geoFiles[i];

        // 更新进度
        const currentProgress = Math.round(((i) / geoFiles.length) * 100);
        setGeoUpdatePercentage(currentProgress);

        // 更新单个文件
        const result = await updateSingleGeoFile(fileName);
        results.push(result);

        // 更新进度状态
        setGeoUpdateProgress([...results]);
      }

      // 完成所有更新
      setGeoUpdatePercentage(100);

      // 显示结果
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;

      let message = `更新完成！\n成功: ${successCount} 个文件\n失败: ${failedCount} 个文件`;

      if (failedCount > 0) {
        const failedFiles = results.filter(r => !r.success);
        message += '\n\n失败详情:\n' + failedFiles.map(f => `${f.fileName}: ${f.error}`).join('\n');
      }

      Alert.alert(
        failedCount === 0 ? '更新成功' : '更新完成',
        message,
        [{ text: '确定' }]
      );

    } catch (error) {
      console.error('Geo update failed:', error);
      Alert.alert('更新失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsUpdatingGeo(false);
      setGeoUpdateProgress([]);
      setGeoUpdatePercentage(0);
    }
  };

  // 渲染规则项
  const renderRuleItem = ({ item, onDragStart, onDragEnd, index }: DragListRenderItemInfo<RouteRule>) => {
    const rule = item;

    const getRuleDescription = (rule: RouteRule): string => {
      const parts: string[] = [];

      if (rule.domain && rule.domain.length > 0) {
        parts.push(`域名: ${rule.domain.slice(0, 2).join(', ')}${rule.domain.length > 2 ? '...' : ''}`);
      }
      if (rule.ip && rule.ip.length > 0) {
        parts.push(`IP: ${rule.ip.slice(0, 2).join(', ')}${rule.ip.length > 2 ? '...' : ''}`);
      }
      if (rule.port) {
        parts.push(`端口: ${rule.port}`);
      }
      if (rule.network) {
        parts.push(`网络: ${rule.network}`);
      }
      if (rule.protocol) {
        parts.push(`协议: ${rule.protocol}`);
      }
      if (rule.inboundTag) {
        parts.push(`入站: ${rule.inboundTag}`);
      }

      return parts.length > 0 ? parts.join(' | ') : '空规则';
    };

    return (
      <View style={styles.cardContainer}>
        <TouchableOpacity
          style={styles.card}
          onPress={() => handleRulePress(index)}
          onPressIn={onDragStart}
          onPressOut={onDragEnd}
        >
          <View style={styles.cardHeader}>
            <Text style={[styles.title, { color: textColor }]}>
              {rule.ruleTag || `规则 ${parseInt(virtualOrder[index]) + 1}`}
            </Text>
            <View style={styles.rightSection}>
              {rule.outboundTag && (
                <Badge style={styles.outboundBadge}>
                  <Text style={styles.badgeText}>{rule.outboundTag}</Text>
                </Badge>
              )}
              <GripVertical size={20} color={textColor + '60'} />
            </View>
          </View>
          <Text style={[styles.ruleDescription, { color: textColor + '80' }]}>
            {getRuleDescription(rule)}
          </Text>
        </TouchableOpacity>
        <View style={[styles.divider, { backgroundColor: borderColor }]} />
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* 固定在顶部的按钮组 */}
      <View style={[styles.headerContainer, { backgroundColor }]}>
        <View style={styles.header}>
          <View style={styles.buttonGroup}>
            <Button
              variant="secondary"
              size="sm"
              onPress={handleAddRule}
              style={styles.button}
            >
              <Plus size={16} color={textColor} />
              <Text style={[styles.buttonText, { color: textColor }]}>添加</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleSave}
              disabled={!hasChanges}
              style={[styles.button, !hasChanges && styles.disabledButton]}
            >
              <Save size={16} color={hasChanges ? textColor : textColor + '60'} />
              <Text style={[styles.buttonText, { color: hasChanges ? textColor : textColor + '60' }]}>保存</Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleRestart}
              disabled={isRestarting}
              style={[styles.button, isRestarting && styles.disabledButton]}
            >
              <RotateCcw size={16} color={isRestarting ? textColor + '60' : textColor} />
              <Text style={[styles.buttonText, { color: isRestarting ? textColor + '60' : textColor }]}>
                重启
              </Text>
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onPress={handleUpdateGeo}
              disabled={isUpdatingGeo}
              style={[styles.button, isUpdatingGeo && styles.disabledButton]}
            >
              <Database size={16} color={isUpdatingGeo ? textColor + '60' : textColor} />
              <Text style={[styles.buttonText, { color: isUpdatingGeo ? textColor + '60' : textColor }]}>
                {isUpdatingGeo ? `更新中 ${geoUpdatePercentage}%` : '更新geo'}
              </Text>
            </Button>
          </View>
        </View>
        <View style={[styles.headerDivider, { backgroundColor: borderColor }]} />
      </View>



      {/* 规则列表 */}
      {rules.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: textColor + '60' }]}>
            暂无路由规则
          </Text>
          <Text style={[styles.emptySubtext, { color: textColor + '40' }]}>
            点击上方添加按钮创建第一条路由规则
          </Text>
        </View>
      ) : (
        <DragList
          data={getOrderedRules()}
          renderItem={renderRuleItem}
          keyExtractor={(_: RouteRule, index: number) => `rule-${virtualOrder[index]}`}
          contentContainerStyle={styles.listContent}
          onReordered={handleReorder}
        />
      )}

      {/* 操作底部弹窗 */}
      <BottomSheetModal
        ref={actionSheetRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor }}
        handleIndicatorStyle={{ backgroundColor: borderColor }}
        enablePanDownToClose={true}
        enableOverDrag={false}
      >
        <BottomSheetView style={[actionSheetStyles.content, { backgroundColor }]}>
          <View style={actionSheetStyles.buttonsContainer}>
            <Button
              variant="ghost"
              onPress={handleEdit}
            >
              <Text style={[actionSheetStyles.buttonText, { color: textColor }]}>
                编辑规则
              </Text>
            </Button>

            <View style={[actionSheetStyles.divider, { backgroundColor: borderColor }]} />

            <Button
              variant="ghost"
              onPress={handleDeleteConfirm}
            >
              <Text style={[actionSheetStyles.buttonText, actionSheetStyles.deleteButtonText]}>
                删除规则
              </Text>
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    // 固定在顶部的容器
  },
  header: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  headerDivider: {
    height: 1,
  },
  list: {
    flex: 1,
  },
  cardContainer: {
    backgroundColor:'white'
  },
  card: {
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  divider: {
    height: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  listContent: {
    paddingTop: 0,
    height:'100%'
  },
  ruleDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 4,
  },
  outboundBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
});

const actionSheetStyles = StyleSheet.create({
  content: {
    padding: 0,
  },
  buttonsContainer: {
    flex: 1,
    justifyContent: 'space-around',
  },
  divider: {
    height: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  deleteButtonText: {
    color: '#ef4444',
  },
});
